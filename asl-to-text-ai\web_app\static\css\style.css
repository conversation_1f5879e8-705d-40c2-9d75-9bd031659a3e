/* ASL-to-Text AI Custom Styles */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --purple-color: #6f42c1;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-brand i {
    margin-right: 8px;
    color: #ffc107;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.hero-image {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

/* Color variants for icons */
.text-purple {
    color: var(--purple-color) !important;
}

/* How It Works Section */
.step-card {
    padding: 2rem;
}

.step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto;
}

/* Statistics Section */
.stat-item {
    padding: 2rem 1rem;
}

.stat-item h3 {
    margin-bottom: 0.5rem;
}

/* Cards */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    border-radius: 10px 10px 0 0 !important;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

/* Video Elements */
.video-container {
    position: relative;
    background: #000;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

#videoElement {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 10px;
}

/* Translation Output */
.translation-output {
    background: linear-gradient(135deg, #f8f9fa, #ffffff);
    border: 2px solid #e9ecef;
    border-radius: 10px;
    min-height: 200px;
    padding: 20px;
    font-size: 1.2rem;
    line-height: 1.6;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.translation-output:hover {
    border-color: #007bff;
    box-shadow: inset 0 2px 4px rgba(0,123,255,0.1);
}

/* Current Word Display */
.current-word {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    padding: 8px 12px;
    border-radius: 6px;
    border: 1px solid #ffeaa7;
    display: inline-block;
    animation: pulse 1s ease-in-out;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Confidence Bar */
.confidence-bar {
    height: 10px;
    background: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
    transition: width 0.5s ease;
    border-radius: 5px;
}

/* Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    animation: pulse-status 2s infinite;
}

@keyframes pulse-status {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.status-connected { 
    background: #28a745;
    box-shadow: 0 0 8px rgba(40, 167, 69, 0.5);
}

.status-processing { 
    background: #ffc107;
    box-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
}

.status-disconnected { 
    background: #dc3545;
    box-shadow: 0 0 8px rgba(220, 53, 69, 0.5);
}

/* Statistics Cards */
.stats-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Upload Area */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #007bff;
    background: #e3f2fd;
}

.upload-area.dragover {
    border-color: #28a745;
    background: #d4edda;
}

/* Progress Bar */
.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* Footer */
footer {
    margin-top: auto;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 50vh;
        text-align: center;
    }
    
    .display-4 {
        font-size: 2rem;
    }
    
    .btn-lg {
        padding: 10px 20px;
        font-size: 1rem;
    }
    
    .stats-card {
        margin-bottom: 10px;
    }
    
    .feature-card {
        margin-bottom: 20px;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .translation-output {
        font-size: 1rem;
        padding: 15px;
    }
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, #007bff, #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-lg {
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.border-gradient {
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #007bff, #0056b3) border-box;
}
