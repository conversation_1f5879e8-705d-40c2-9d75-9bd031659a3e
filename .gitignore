# Python
__pycache__/
*.py[cod]
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
venv_*/
ENV/
env/

# Data and models - NEVER COMMIT
data/
datasets/
models/
instance/
results/
temp/
tmp/
cache/
downloads/

# ASL specific
asl-to-text-ai/data/
asl-to-text-ai/models/

# Large files
*.npy
*.npz
*.pkl
*.pickle
*.joblib
*.h5
*.hdf5
*.pt
*.pth
*.ckpt
*.pb
*.tflite
*.onnx
*.db
*.sqlite
*.sqlite3

# Media files
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv
*.webm
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.mp3
*.wav

# Archives
*.zip
*.tar
*.gz
*.bz2
*.rar
*.7z

# Large text files
*.csv
*.tsv
*.json
*.xml
*.yaml
*.yml

# Logs
*.log
logs/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Kaggle
kaggle.json
.kaggle/

# Jupyter
.ipynb_checkpoints/

# Specific problematic directories
wlasl_extracted/
kaggle_download/
frames/
videos/