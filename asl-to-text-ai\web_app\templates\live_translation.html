<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live ASL Translation - ASL-to-Text AI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    <style>
        .video-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
        }
        
        #videoElement {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .translation-output {
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            min-height: 200px;
            padding: 20px;
            font-size: 1.2rem;
            line-height: 1.6;
        }
        
        .current-word {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            border: 1px solid #ffeaa7;
        }
        
        .confidence-bar {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc3545, #ffc107, #28a745);
            transition: width 0.3s ease;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-connected { background: #28a745; }
        .status-processing { background: #ffc107; }
        .status-disconnected { background: #dc3545; }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="fas fa-hands"></i> ASL-to-Text AI
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">Home</a>
                <a class="nav-link active" href="/live">Live Translation</a>
                <a class="nav-link" href="/upload">Upload Video</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <div class="row">
            <!-- Video Feed Column -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-video"></i> Live Video Feed
                        </h5>
                        <div class="d-flex align-items-center">
                            <span class="status-indicator" id="connectionStatus"></span>
                            <span id="connectionText">Disconnected</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="video-container mb-3">
                            <video id="videoElement" autoplay muted playsinline></video>
                            <canvas id="canvasElement" style="display: none;"></canvas>
                        </div>
                        
                        <div class="d-flex gap-2 justify-content-center">
                            <button id="startBtn" class="btn btn-success btn-lg">
                                <i class="fas fa-play"></i> Start Translation
                            </button>
                            <button id="stopBtn" class="btn btn-danger btn-lg" disabled>
                                <i class="fas fa-stop"></i> Stop Translation
                            </button>
                            <button id="clearBtn" class="btn btn-secondary btn-lg">
                                <i class="fas fa-trash"></i> Clear Text
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Translation Output Column -->
            <div class="col-lg-4">
                <!-- Current Translation -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-comment-dots"></i> Translation Output
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="translation-output" id="translationOutput">
                            <p class="text-muted text-center">
                                <i class="fas fa-hand-paper fa-2x mb-2"></i><br>
                                Start signing to see translation here
                            </p>
                        </div>
                        
                        <!-- Current Word Display -->
                        <div class="mt-3">
                            <small class="text-muted">Current Word:</small>
                            <div id="currentWord" class="current-word d-none">
                                <span id="currentWordText">-</span>
                                <small class="text-muted ms-2">(<span id="currentWordConfidence">0</span>%)</small>
                            </div>
                        </div>
                        
                        <!-- Confidence Bar -->
                        <div class="mt-3">
                            <small class="text-muted">Detection Confidence:</small>
                            <div class="confidence-bar">
                                <div class="confidence-fill" id="confidenceBar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line"></i> Session Statistics
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <span>Words Detected:</span>
                                <strong id="wordsDetected">0</strong>
                            </div>
                        </div>
                        
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <span>Average Confidence:</span>
                                <strong id="avgConfidence">0%</strong>
                            </div>
                        </div>
                        
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <span>Processing Time:</span>
                                <strong id="processingTime">0ms</strong>
                            </div>
                        </div>
                        
                        <div class="stats-card">
                            <div class="d-flex justify-content-between">
                                <span>Buffer Status:</span>
                                <strong id="bufferStatus">0/30</strong>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="{{ url_for('static', filename='js/live_translation.js') }}"></script>
</body>
</html>
