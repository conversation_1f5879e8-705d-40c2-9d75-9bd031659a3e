# Fly.io configuration for ASL-to-Text AI
app = "asl-to-text-ai"
primary_region = "ord"

[build]

[env]
  FLASK_ENV = "production"
  DEBUG = "false"
  PYTHONPATH = "/app/src"

[http_service]
  internal_port = 5000
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 1024

[checks]
  [checks.health]
    grace_period = "30s"
    interval = "15s"
    method = "GET"
    path = "/api/health"
    port = 5000
    timeout = "10s"
