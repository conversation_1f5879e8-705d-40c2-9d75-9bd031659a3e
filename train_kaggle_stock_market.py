#!/usr/bin/env python3
"""
Kaggle Stock Market Training
Uses Kaggle datasets for comprehensive, reliable stock market data
"""

import logging
import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from neural_network_predictor import StockNeuralNetworkPredictor
from app import app, db, Stock
import subprocess

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KaggleStockMarketTrainer:
    def __init__(self):
        self.predictor = StockNeuralNetworkPredictor()
        self.kaggle_datasets = [
            'jacksoncrow/stock-market-dataset',
            'borismarjanovic/price-volume-data-for-all-us-stocks-etfs',
            'dgawlik/nyse',
            'qks1lver/amex-nyse-nasdaq-stock-histories',
            'paultimothymooney/stock-market-data'
        ]
        
    def setup_kaggle(self):
        """Setup Kaggle API and authentication"""
        logger.info("Setting up Kaggle API...")
        
        # Check if kaggle.json exists
        kaggle_json_path = r"c:\Users\<USER>\Downloads\kaggle.json"
        kaggle_config_dir = os.path.expanduser("~/.kaggle")
        kaggle_config_path = os.path.join(kaggle_config_dir, "kaggle.json")
        
        try:
            # Create .kaggle directory if it doesn't exist
            os.makedirs(kaggle_config_dir, exist_ok=True)
            
            # Copy kaggle.json to the correct location
            if os.path.exists(kaggle_json_path):
                import shutil
                shutil.copy2(kaggle_json_path, kaggle_config_path)
                
                # Set proper permissions (important for Kaggle API)
                if os.name != 'nt':  # Not Windows
                    os.chmod(kaggle_config_path, 0o600)
                
                logger.info("Kaggle credentials configured successfully")
                return True
            else:
                logger.error(f"Kaggle.json not found at {kaggle_json_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error setting up Kaggle: {e}")
            return False
    
    def install_kaggle_api(self):
        """Install Kaggle API if not already installed"""
        try:
            import kaggle
            logger.info("Kaggle API already installed")
            return True
        except ImportError:
            logger.info("Installing Kaggle API...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "kaggle"])
                logger.info("Kaggle API installed successfully")
                return True
            except subprocess.CalledProcessError as e:
                logger.error(f"Failed to install Kaggle API: {e}")
                return False
    
    def download_stock_datasets(self):
        """Download comprehensive stock market datasets from Kaggle"""
        logger.info("Downloading stock market datasets from Kaggle...")
        
        if not self.install_kaggle_api():
            return False
            
        if not self.setup_kaggle():
            return False
        
        try:
            import kaggle
            
            # Create data directory
            os.makedirs('kaggle_data', exist_ok=True)
            
            downloaded_datasets = []
            
            for dataset in self.kaggle_datasets:
                try:
                    logger.info(f"Downloading {dataset}...")
                    kaggle.api.dataset_download_files(
                        dataset, 
                        path=f'kaggle_data/{dataset.replace("/", "_")}',
                        unzip=True
                    )
                    downloaded_datasets.append(dataset)
                    logger.info(f"Successfully downloaded {dataset}")
                    
                except Exception as e:
                    logger.warning(f"Failed to download {dataset}: {e}")
                    continue
            
            logger.info(f"Downloaded {len(downloaded_datasets)} datasets successfully")
            return len(downloaded_datasets) > 0
            
        except Exception as e:
            logger.error(f"Error downloading datasets: {e}")
            return False
    
    def process_kaggle_stock_data(self):
        """Process downloaded Kaggle stock data into training format"""
        logger.info("Processing Kaggle stock market data...")
        
        all_stocks = []
        
        # Process each downloaded dataset
        for root, dirs, files in os.walk('kaggle_data'):
            for file in files:
                if file.endswith('.csv'):
                    file_path = os.path.join(root, file)
                    try:
                        df = pd.read_csv(file_path)
                        
                        # Try to identify stock symbols from the data
                        symbols = self._extract_symbols_from_dataset(df, file)
                        all_stocks.extend(symbols)
                        
                    except Exception as e:
                        logger.debug(f"Could not process {file}: {e}")
                        continue
        
        # Remove duplicates and filter valid symbols
        unique_stocks = list(set(all_stocks))
        valid_stocks = [s for s in unique_stocks if self._is_valid_stock_symbol(s)]
        
        logger.info(f"Extracted {len(valid_stocks)} unique stock symbols from Kaggle datasets")
        return valid_stocks
    
    def _extract_symbols_from_dataset(self, df, filename):
        """Extract stock symbols from a dataset"""
        symbols = []
        
        # Common column names for stock symbols
        symbol_columns = ['Symbol', 'symbol', 'ticker', 'Ticker', 'stock', 'Stock', 'Name']
        
        for col in symbol_columns:
            if col in df.columns:
                symbols.extend(df[col].dropna().unique().tolist())
                break
        
        # If no symbol column found, try to extract from filename
        if not symbols and filename:
            # Many Kaggle datasets name files after stock symbols
            base_name = os.path.splitext(filename)[0].upper()
            if self._is_valid_stock_symbol(base_name):
                symbols.append(base_name)
        
        return symbols
    
    def _is_valid_stock_symbol(self, symbol):
        """Check if a symbol is a valid stock symbol"""
        if not symbol or not isinstance(symbol, str):
            return False
        
        symbol = str(symbol).strip().upper()
        
        # Basic validation
        if len(symbol) < 1 or len(symbol) > 5:
            return False
        
        # Must be alphanumeric (allowing hyphens for some stocks like BRK-A)
        if not symbol.replace('-', '').replace('.', '').isalnum():
            return False
        
        # Exclude obvious non-stock symbols
        exclude_patterns = ['INDEX', 'TOTAL', 'AVERAGE', 'SUM', 'COUNT', 'NULL', 'NAN']
        if any(pattern in symbol for pattern in exclude_patterns):
            return False
        
        return True
    
    def train_on_kaggle_data(self, target_accuracy=85.0):
        """Train neural network on Kaggle stock market data"""
        logger.info("Starting Kaggle stock market training...")
        
        # Download and process Kaggle data
        if not self.download_stock_datasets():
            logger.error("Failed to download Kaggle datasets")
            return False
        
        # Extract stock symbols
        stock_symbols = self.process_kaggle_stock_data()
        
        if len(stock_symbols) < 100:
            logger.error("Not enough stock symbols extracted from Kaggle data")
            return False
        
        logger.info(f"Training on {len(stock_symbols)} stocks from Kaggle datasets")
        
        # Train in progressive batches
        batch_size = 250
        overall_accuracy = 0.0
        successful_batches = 0
        
        for i in range(0, len(stock_symbols), batch_size):
            batch_symbols = stock_symbols[i:i+batch_size]
            batch_num = i // batch_size + 1
            total_batches = (len(stock_symbols) + batch_size - 1) // batch_size
            
            logger.info(f"\n{'='*60}")
            logger.info(f"Training Batch {batch_num}/{total_batches}: {len(batch_symbols)} stocks")
            logger.info(f"Sample symbols: {batch_symbols[:10]}")
            logger.info(f"{'='*60}")
            
            try:
                with app.app_context():
                    result = self.predictor.train(
                        symbols_list=batch_symbols,
                        epochs=25,
                        validation_split=0.2,
                        save_model=True,
                        learning_rate=0.001
                    )
                
                # Test this batch
                batch_accuracy = self._test_batch_accuracy(batch_symbols[:15])
                logger.info(f"Batch {batch_num} accuracy: {batch_accuracy:.2f}%")
                
                if batch_accuracy > 0:  # Only count successful batches
                    overall_accuracy = ((overall_accuracy * successful_batches) + batch_accuracy) / (successful_batches + 1)
                    successful_batches += 1
                
                logger.info(f"Overall accuracy so far: {overall_accuracy:.2f}% (from {successful_batches} successful batches)")
                
                if overall_accuracy >= target_accuracy and successful_batches >= 3:
                    logger.info(f"🎉 Target accuracy {target_accuracy}% achieved!")
                    break
                    
            except Exception as e:
                logger.error(f"Error training batch {batch_num}: {e}")
                continue
        
        # Final comprehensive test
        final_accuracy = self._comprehensive_test(stock_symbols[:50])
        
        # Save detailed report
        self._save_kaggle_training_report(stock_symbols, overall_accuracy, final_accuracy, successful_batches)
        
        logger.info(f"\n{'='*60}")
        logger.info(f"KAGGLE STOCK MARKET TRAINING COMPLETE")
        logger.info(f"{'='*60}")
        logger.info(f"Total stocks from Kaggle: {len(stock_symbols)}")
        logger.info(f"Successful training batches: {successful_batches}")
        logger.info(f"Overall accuracy: {overall_accuracy:.2f}%")
        logger.info(f"Final test accuracy: {final_accuracy:.2f}%")
        logger.info(f"Target achieved: {'YES' if overall_accuracy >= target_accuracy else 'NO'}")
        
        return overall_accuracy >= target_accuracy
    
    def _test_batch_accuracy(self, test_symbols):
        """Test accuracy on a batch"""
        if not self.predictor.is_trained:
            return 0.0
        
        successful_predictions = 0
        total_predictions = 0
        
        with app.app_context():
            for symbol in test_symbols:
                try:
                    prediction = self.predictor.predict(symbol)
                    total_predictions += 1
                    
                    if prediction and 'prediction' in prediction:
                        successful_predictions += 1
                        
                except Exception as e:
                    total_predictions += 1
        
        accuracy = (successful_predictions / total_predictions * 100) if total_predictions > 0 else 0.0
        logger.info(f"Batch test: {successful_predictions}/{total_predictions} successful ({accuracy:.1f}%)")
        
        return accuracy
    
    def _comprehensive_test(self, test_symbols):
        """Comprehensive test with detailed metrics"""
        logger.info("Running comprehensive Kaggle data test...")
        
        results = {
            'total_tests': 0,
            'successful_predictions': 0,
            'prediction_breakdown': {},
            'confidence_scores': []
        }
        
        with app.app_context():
            for symbol in test_symbols:
                try:
                    prediction = self.predictor.predict(symbol)
                    results['total_tests'] += 1
                    
                    if prediction and 'prediction' in prediction:
                        results['successful_predictions'] += 1
                        pred_class = prediction['prediction']
                        confidence = prediction.get('confidence', 0)
                        
                        if pred_class not in results['prediction_breakdown']:
                            results['prediction_breakdown'][pred_class] = 0
                        results['prediction_breakdown'][pred_class] += 1
                        results['confidence_scores'].append(confidence)
                        
                except Exception as e:
                    results['total_tests'] += 1
        
        accuracy = (results['successful_predictions'] / results['total_tests'] * 100) if results['total_tests'] > 0 else 0.0
        avg_confidence = np.mean(results['confidence_scores']) if results['confidence_scores'] else 0.0
        
        logger.info(f"Comprehensive test accuracy: {accuracy:.2f}%")
        logger.info(f"Average confidence: {avg_confidence:.2f}%")
        logger.info(f"Prediction breakdown: {results['prediction_breakdown']}")
        
        return accuracy
    
    def _save_kaggle_training_report(self, trained_symbols, overall_accuracy, final_accuracy, successful_batches):
        """Save detailed Kaggle training report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        report = {
            'timestamp': timestamp,
            'training_type': 'KAGGLE_STOCK_MARKET',
            'data_source': 'Kaggle Datasets',
            'datasets_used': self.kaggle_datasets,
            'total_symbols_from_kaggle': len(trained_symbols),
            'successful_training_batches': successful_batches,
            'overall_accuracy': overall_accuracy,
            'final_test_accuracy': final_accuracy,
            'sample_symbols': trained_symbols[:50],  # First 50 as sample
            'model_info': {
                'features_count': len(self.predictor.feature_names) if self.predictor.feature_names else 0,
                'model_file': 'models/stock_nn_model.pth',
                'is_trained': self.predictor.is_trained
            }
        }
        
        os.makedirs('reports', exist_ok=True)
        report_file = f"reports/kaggle_stock_training_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Kaggle training report saved to: {report_file}")

def main():
    logger.info("Starting Kaggle stock market training...")
    
    trainer = KaggleStockMarketTrainer()
    success = trainer.train_on_kaggle_data(target_accuracy=80.0)
    
    if success:
        logger.info("🎉 KAGGLE STOCK MARKET TRAINING SUCCESSFUL!")
        sys.exit(0)
    else:
        logger.info("Training completed but target accuracy not achieved")
        sys.exit(1)

if __name__ == "__main__":
    main()
